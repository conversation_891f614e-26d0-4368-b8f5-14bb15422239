<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use admin\models\ArticleAttribute;
use admin\models\Company;
use admin\models\Job;
use admin\models\Major;
use common\base\BaseActiveRecord;
use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseAnnouncementEducationRelation;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobCategoryRelation;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\helpers\DebugHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use yii\db\Expression;

/**
 * 公告列表
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class IndexService extends BaseService
{
    // 渠道业务
    /** @var string 非合作 */
    const CHANNEL_UNCOOPERATION = 'unCooperation';
    /** @var string 合作 */
    const CHANNEL_COOPERATION = 'cooperation';
    /** @var string 非合作审核 */
    const CHANNEL_UNCOOPERATION_AUDIT = 'unCooperationAudit';
    /** @var string 合作审核 */
    const CHANNEL_COOPERATION_AUDIT = 'cooperationAudit';

    public function __construct($channel = null)
    {
        $this->channel = $channel;
        parent::__construct();
    }

    /**
     * 获取合作单位公告审核列表
     * @param $params
     * @return array
     */
    public function cooperationAuditRun($params)
    {
        return $this->getList($params, [
            'and',
            [
                '=',
                'c.is_cooperation',
                BaseCompany::COOPERATIVE_UNIT_YES,
            ],
            [
                '=',
                'a.audit_status',
                BaseAnnouncement::STATUS_AUDIT_AWAIT,
            ],
            [
                '<>',
                'a.status',
                BaseAnnouncement::STATUS_DELETE,
            ],
        ]);
    }

    /**
     * 获取非合作单位公告审核列表
     * @param $params
     * @return array
     */
    public function unCooperationAuditRun($params)
    {
        return $this->getList($params, [
            'and',
            [
                '=',
                'c.is_cooperation',
                BaseCompany::COOPERATIVE_UNIT_NO,
            ],
            [
                '=',
                'a.audit_status',
                BaseAnnouncement::STATUS_AUDIT_AWAIT,
            ],
            [
                '<>',
                'a.status',
                BaseAnnouncement::STATUS_DELETE,
            ],
        ]);
    }

    /**
     * 获取非合作单位公告列表
     * @param $params
     * @return array
     */
    public function unCooperationRun($params)
    {
        return $this->getList($params, [
            'and',
            [
                '=',
                'c.is_cooperation',
                BaseCompany::COOPERATIVE_UNIT_NO,
            ],
            [
                '<>',
                'a.audit_status',
                BaseAnnouncement::STATUS_AUDIT_AWAIT,
            ],
            [
                '<>',
                'a.status',
                BaseAnnouncement::STATUS_DELETE,
            ],
        ]);
    }

    /**
     * 获取合作单位公告列表
     * @param $params
     * @return array
     */
    public function cooperationRun($params)
    {
        return $this->getList($params, [
            'and',
            [
                '=',
                'c.is_cooperation',
                BaseCompany::COOPERATIVE_UNIT_YES,
            ],
            [
                '<>',
                'a.audit_status',
                BaseAnnouncement::STATUS_AUDIT_AWAIT,
            ],
            [
                '<>',
                'a.status',
                BaseAnnouncement::STATUS_DELETE,
            ],
        ]);
    }

    private function formatIndex($list)
    {
        // 公告属性
        $articleAttribute = [];
        // 公告栏目
        $homeColumn = [];
        // 创建人
        $creator = [];
        // 公告所有专业
        $announcementMajor = [];
        // 公告待审核职位数量
        $auditWaitJob = [];
        if ($list) {
            $announcementIds = array_column($list, 'announcement_id');

            // 投递相关统计
            //            // 平台投递
            //            'platformNum'      => intval($item['platform_num']),
            //                // 邮箱投递
            //                'emailNum'         => intval($item['email_num']),
            //                // 网址投递
            //                'linkNum'          => intval($item['link_num']),
            //                // 面试邀约
            //                'interviewNum'     => intval($item['interview_num']),

            // 平台投递
            $deliveryWayPlatform = BaseJobApplyRecordExtra::getAnnouncementAmount($announcementIds,
                'delivery_way_platform');
            // 邮箱投递
            $deliveryWayEmail = BaseJobApplyRecordExtra::getAnnouncementAmount($announcementIds, 'delivery_way_email');
            // 网址投递
            $deliveryWayLink = BaseJobApplyRecordExtra::getAnnouncementAmount($announcementIds, 'delivery_way_link');
            // 面试邀约
            $interview = BaseJobApplyRecordExtra::getAnnouncementAmount($announcementIds, 'interview');
            // 投递次数
            $deliveryNum = BaseJobApplyRecordExtra::getAnnouncementAmount($announcementIds, 'total');

            // 公告属性
            $articleIds           = array_column($list, 'article_id');
            $articleAttributeList = BaseArticleAttribute::find()
                ->where([
                    'in',
                    'article_id',
                    $articleIds,
                ])
                ->select([
                    'type',
                    'article_id',
                ])
                ->asArray()
                ->all();
            foreach ($articleAttributeList as $attributeInfo) {
                $articleAttribute[$attributeInfo['article_id']][] = ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$attributeInfo['type']];
            }
            foreach ($articleAttribute as $key => $item) {
                $articleAttribute[$key] = implode(' ', $item);
            }

            // 公告栏目
            $homeColumnIds    = array_column($list, 'home_column_id');
            $homeSubColumnIds = array_column($list, 'home_sub_column_ids');
            foreach ($homeSubColumnIds as $homeSubColumnId) {
                $homeColumnIds = array_merge($homeColumnIds, explode(',', $homeSubColumnId));
            }

            $homeColumnIds  = array_unique($homeColumnIds);
            $homeColumnList = BaseHomeColumn::find()
                ->where([
                    'in',
                    'id',
                    $homeColumnIds,
                ])
                ->select([
                    'id',
                    'name',
                ])
                ->asArray()
                ->all();

            $homeColumn = array_column($homeColumnList, 'name', 'id');
            // 计算公告人数
            $announcementJobList = $this->getJobList($announcementIds);
            // 创建人
            $creatorIds  = array_column($list, 'creator_id');
            $creatorList = BaseAdmin::find()
                ->where([
                    'in',
                    'id',
                    $creatorIds,
                ])
                ->select([
                    'id',
                    'name',
                ])
                ->asArray()
                ->all();

            $creator = array_column($creatorList, 'name', 'id');

            // 公告的专业
            $announcementMajor = $this->getJobMajor($announcementIds);

            // 审核相关，才需要计算待审核职位
            if ($this->channel == self::CHANNEL_UNCOOPERATION_AUDIT || $this->channel == self::CHANNEL_COOPERATION_AUDIT) {
                $auditWaitJob = BaseJob::find()
                    ->where([
                        'and',
                        [
                            'in',
                            'announcement_id',
                            $announcementIds,
                        ],
                        [
                            '<>',
                            'status',
                            BaseJob::STATUS_DELETE,
                        ],
                        [
                            '=',
                            'audit_status',
                            BaseJob::AUDIT_STATUS_WAIT,
                        ],
                    ])
                    ->select([
                        'announcement_id',
                        'COUNT(*) as job_num',
                    ])
                    ->groupBy('announcement_id')
                    ->asArray()
                    ->all();

                if ($auditWaitJob) {
                    $auditWaitJob = array_column($auditWaitJob, 'job_num', 'announcement_id');
                }
            }
        }

        // 按钮组
        $btnKeys = [];
        switch ($this->channel) {
            case self::CHANNEL_UNCOOPERATION:
                $btnKeys = [
                    'edit',
                    'hide',
                    'show',
                    'republish',
                    'offline',
                    'refresh',
                    'delete',
                    'attribute',
                ];
                break;
            case self::CHANNEL_COOPERATION:
                $btnKeys = [
                    'edit',
                    'hide',
                    'show',
                    'log',
                    'refresh',
                    'republish',
                    'offline',
                    'business',
                    'delete',
                    'attribute',
                ];
                break;
            case self::CHANNEL_COOPERATION_AUDIT:
            case self::CHANNEL_UNCOOPERATION_AUDIT:
                break;
        }

        $dateFields = [
            'first_release_time',
            'refresh_time',
            'real_refresh_time',
            'offline_time',
            'add_time',
            'apply_audit_time',
        ];
        $formatList = [];
        foreach ($list as $item) {
            $item['offline_time'] = (($item['status'] == BaseAnnouncement::STATUS_OFFLINE) ? $item['offline_time'] : $item['period_date']);

            $homeColumnTextArr    = [];
            $currentHomeColumnIds = array_merge([$item['home_column_id']], explode(',', $item['home_sub_column_ids']));
            foreach ($currentHomeColumnIds as $homeColumnId) {
                $homeColumnTextArr[] = $homeColumn[$homeColumnId] ?? '';
            }

            $btnList = $this->generateButtonList($item, $btnKeys);

            $data = [
                'announcementUuId' => $item['uuid'],
                'announcementId'   => $item['announcement_id'],
                'articleId'        => $item['article_id'],
                // 公告标题
                'title'            => $item['title'],
                // 公告属性
                'tip'              => $articleAttribute[$item['article_id']] ?? '',
                // 所属单位
                'companyName'      => $item['company_name'],
                // 所属单位id
                'companyId'        => $item['company_id'],
                // 所属栏目
                'allHomeColumnTxt' => implode(',', $homeColumnTextArr),
                // 所属主栏目
                'homeColumnTxt'    => $homeColumn[$item['home_column_id']] ?? '',
                // 招聘人数
                'amountCount'      => strval($announcementJobList[$item['announcement_id']]['amount'] ?? '0'),
                // 招聘职位
                'allJobAmount'     => $item['all_job_amount'],
                // 在线职位
                'onlineJobAmount'  => $item['online_job_amount'],
                // 下线职位
                'offlineJobAmount' => $announcementJobList[$item['announcement_id']]['offlineJobAmount'] ?? 0,
                // 平台投递
                'platformNum'      => intval($deliveryWayPlatform[$item['announcement_id']] ?? 0),
                // 邮箱投递
                'emailNum'         => intval($deliveryWayEmail[$item['announcement_id']] ?? 0),
                // 网址投递
                'linkNum'          => intval($deliveryWayLink[$item['announcement_id']] ?? 0),
                // 面试邀约
                'interviewNum'     => intval($interview[$item['announcement_id']] ?? 0),
                // 点击量
                'click'            => intval($item['click']),
                // 招聘状态
                'statusTxt'        => BaseAnnouncement::STATUS_LIST[$item['status']] ?? '未知',
                'status'           => $item['status'],
                // 审核状态
                'auditStatusTxt'   => BaseAnnouncement::STATUS_AUDIT_LIST[$item['audit_status']] ?? '未知',
                'auditStatus'      => $item['audit_status'],
                // 创建人
                'creator'          => $creator[$item['creator_id']] ?? '',
                // 学科专业
                'majorTxt'         => $announcementMajor[$item['announcement_id']] ?? '',
                // 排序
                'homeSort'         => $item['home_sort'],
                // 是否是小程序
                'isMiniapp'        => $item['is_miniapp'],
                // 显示状态
                'isShowTxt'        => BaseArticle::IS_SHOW_LIST[$item['is_show']] ?: '',
                // 审核人
                'auditAdminName'   => $item['audit_admin_name'],
                // 申请人
                'applyAdminName'   => $item['apply_admin_name'],
                // 待审核数量
                'auditWaitJob'     => $auditWaitJob[$item['announcement_id']] ?? 0,
                'btnList'          => $btnList,
                // 投递次数
                'deliveryNum'      => $deliveryNum[$item['announcement_id']] ?? 0,
            ];

            // 计算日期逻辑
            foreach ($dateFields as $field) {
                $data[$field . 'Date'] = $this->formatTime($item[$field]);
                $data[$field]          = ($item[$field] == TimeHelper::ZERO_TIME) ? '-' : $item[$field];
            }

            $formatList[] = $data;
        }

        return $formatList;
    }

    private function getJobMajor($announcementIds)
    {
        $majorList = BaseJobMajorRelation::find()
            ->alias('jmr')
            ->innerJoin([
                'm' => BaseMajor::tableName(),
                'm.id = jmr.major_id',
            ])
            ->where([
                'in',
                'jmr.announcement_id',
                $announcementIds,
            ])
            ->select([
                'jmr.announcement_id',
                'm.name',
            ])
            ->groupBy('m.id')
            ->asArray()
            ->all();

        if (!$majorList) {
            return [];
        }

        $announcementMajorList = [];
        foreach ($majorList as $item) {
            $announcementMajorList[$item['announcement_id']][] = $item['name'];
        }

        foreach ($announcementMajorList as $announcementId => $value) {
            $announcementMajorList[$announcementId] = implode(',', $value);
        }

        return $announcementMajorList;
    }

    private function getJobList($announcementIds)
    {
        $jobList = BaseJob::find()
            ->select([
                'id',
                'amount',
                'announcement_id',
                'status',
                'is_show',
            ])
            ->andWhere([
                'and',
                [
                    '<>',
                    'status',
                    BaseJob::STATUS_DELETE,
                ],
                [
                    'in',
                    'announcement_id',
                    $announcementIds,
                ],
            ])
            ->asArray()
            ->all();

        $jobList = array_column($jobList, null, 'id');

        $announcementJobAmountList  = [];
        $announcementJobOfflineList = [];

        $unknownAmount = [];
        foreach ($jobList as $item) {
            if ($item['amount'] == '若干') {
                $unknownAmount[] = $item['announcement_id'];
            }
            $announcementJobAmountList[$item['announcement_id']][] = $item['amount'];

            // 如果状态是下线，并且显示状态是显示
            if ($item['status'] == BaseJob::STATUS_OFFLINE && $item['is_show'] == BaseJob::IS_SHOW_YES) {
                $announcementJobOfflineList[$item['announcement_id']][] = $item['id'];
            }
        }

        $announcementJobList = [];
        foreach ($announcementIds as $announcementId) {
            if (isset($announcementJobList[$announcementId])) {
                $announcementJobList[$announcementId] = [
                    'amount'           => 0,
                    'offlineJobAmount' => 0,
                ];
            }

            $announcementJobList[$announcementId]['amount'] = array_sum($announcementJobAmountList[$announcementId] ?? []);
            if (in_array($announcementId, $unknownAmount)) {
                $announcementJobList[$announcementId]['amount'] = '若干';
            }

            $announcementJobList[$announcementId]['offlineJobAmount'] = count($announcementJobOfflineList[$announcementId] ?? []);
        }

        return $announcementJobList;
    }

    public function getList($params, $defaultWhere = [])
    {
        $query = $this->buildBaseQuery($params);
        if ($defaultWhere) {
            $query->andWhere($defaultWhere);
        }

        $count    = $query->count();
        $pageSize = $params['limit'] ?: \Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), '获取公告列表');

        // 返回的列表顺序
        $list = $this->formatIndex($list);

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'],
            ],
        ];
    }

    public function buildBaseQuery($params)
    {
        // 查询字段
        $selectField = [
            // 公告编号和id和文章id
            'ar.id as article_id',
            'a.uuid',
            'a.id as announcement_id',
            // 公告标题
            'a.title',
            // 单位名称
            'c.full_name as company_name',
            'c.id as company_id',
            // 招聘职位（数量）
            'a.all_job_amount',
            // 在线职位
            'a.online_job_amount',
            // 点击量
            'ar.click',
            // 招聘状态
            'a.status',
            // 审核状态
            'a.audit_status',
            // 所属主栏目
            'ar.home_column_id',
            // 所属子栏目
            'ar.home_sub_column_ids',
            // 初次发布时间
            'ar.first_release_time',
            // 发布时间
            'ar.refresh_time',
            // 刷新时间
            'ar.real_refresh_time',
            // 创建人
            'a.creator_id',
            // 排序
            'a.home_sort',
            // 是否小程序
            'a.is_miniapp',
            // 显示状态
            'ar.is_show',
            // 审核人
            'a.audit_admin_name',
            // 申请人
            'a.apply_admin_name',
            // 下线时间
            'a.offline_time',
            'a.period_date',
            // 创建时间
            'a.add_time',
            // 申请审核时间
            'ar.apply_audit_time',
        ];

        // 平台投递
        $isAddJare = false;
        if (isset($params['sortPlatformNum']) && $params['sortPlatformNum'] != '') {
            $selectField[] = 'SUM(jare.delivery_way_platform) as platform_num';
            $isAddJare     = true;
        }
        // 邮箱投递
        if (isset($params['sortEmailNum']) && $params['sortEmailNum'] != '') {
            $selectField[] = 'SUM(jare.delivery_way_email) as email_num';
            $isAddJare     = true;
        }
        // 网址投递
        if (isset($params['sortLinkNum']) && $params['sortLinkNum'] != '') {
            $selectField[] = 'SUM(jare.delivery_way_link) as link_num';
            $isAddJare     = true;
        }
        // 面试邀约
        if (isset($params['sortInterviewNum']) && $params['sortInterviewNum'] != '') {
            $selectField[] = 'SUM(jare.interview) as interview_num';
            $isAddJare     = true;
        }

        $query = BaseAnnouncement::find()
            ->alias('a')
            ->select($selectField)
            ->groupBy('a.id')
            ->innerJoin(['ar' => BaseArticle::tableName()], 'a.article_id = ar.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = a.company_id ');

        $order = BaseActiveRecord::getOrderByInParams($params, [
            [
                // 招聘人数
                'sortAllJobAmount',
                'a.all_job_amount',
            ],
            [
                // 在线职位
                'sortOnlineJobAmount',
                'a.online_job_amount',
            ],
            [
                // 平台投递
                'sortPlatformNum',
                'platform_num',
            ],
            [
                // 邮箱投递
                'sortEmailNum',
                'email_num',
            ],
            [
                // 网址投递
                'sortLinkNum',
                'link_num',
            ],
            [
                // 面试邀约
                'sortInterviewNum',
                'interview_num',
            ],
            [
                // 点击量
                'sortClick',
                'ar.click',
            ],
            [
                // 初次发布时间
                'sortFirstReleaseTime',
                'ar.first_release_time',
            ],
            [
                // 发布时间
                'sortRefreshTime',
                'ar.refresh_time',
            ],
            [
                // 刷新时间
                'sortRealRefreshTime',
                'ar.real_refresh_time',
            ],
        ]);

        $order[] = 'a.id desc';
        $orderBy = implode(',', $order);

        // 设置where条件
        $query->orderBy($orderBy);

        // where 条件
        $andWhere = ['and',];

        // ------- join信息
        if (isset($params['announcementAttribute']) && $params['announcementAttribute'] != '') {
            $query->innerJoin(['aa' => ArticleAttribute::tableName()], 'aa.article_id = ar.id');
        }

        if (isset($params['majorId']) && $params['majorId'] != '') {
            $query->innerJoin(['jmr' => BaseJobMajorRelation::tableName()], 'a.id = jmr.announcement_id');
        }
        if (isset($params['educationType']) && $params['educationType'] != '') {
            $query->innerJoin(['aer' => BaseAnnouncementEducationRelation::tableName()], 'a.id = aer.announcement_id');
        }
        if (isset($params['jobCategoryId']) && $params['jobCategoryId'] != '') {
            $query->innerJoin(['jcr' => BaseJobCategoryRelation::tableName()], 'a.id = jcr.announcement_id');
        }
        if (isset($params['city']) && $params['city'] != '') {
            $query->innerJoin(['aar' => BaseAnnouncementAreaRelation::tableName()], 'a.id = aar.announcement_id');
        }
        if (isset($params['columnId']) && $params['columnId'] != '') {
            $query->innerJoin(['ac' => BaseArticleColumn::tableName()], 'ac.article_id = ar.id');
        }
        if ($isAddJare) {
            $query->leftJoin(['jare' => BaseJobApplyRecordExtra::tableName()], 'a.id = jare.announcement_id');
        }

        // -------- where条件
        // 创建人
        if (isset($params['creator']) && $params['creator'] != '') {
            $adminId = BaseAdmin::find()
                ->where([
                    'like',
                    'name',
                    $params['creator'],
                ])
                ->select(['id'])
                ->column();
            if (!$adminId) {
                $adminId[] = -1;
            }

            $andWhere[] = [
                'in',
                'a.creator_id',
                $adminId,
            ];
        }
        if (isset($params['announcementTitleNum']) && $params['announcementTitleNum'] != '') {
            $andWhere[] = [
                'or',
                [
                    'like',
                    'a.title',
                    $params['announcementTitleNum'],
                ],
                [
                    'like',
                    'a.uuid',
                    $params['announcementTitleNum'],
                ],
            ];
        }

        // 单位检索
        if (isset($params['companyName']) && $params['companyName'] != '') {
            $andWhere[] = [
                'or',
                [
                    'like',
                    'c.full_name',
                    $params['companyName'],
                ],
                [
                    'like',
                    'c.uuid',
                    $params['companyName'],
                ],
            ];
        }

        // 是否是合作单位
        if (isset($params['isCooperation']) && $params['isCooperation'] != '') {
            $andWhere[] = [
                'in',
                'c.is_cooperation',
                StringHelper::changeStrToFilterArr($params['isCooperation']),
            ];
        }

        // 公告主栏目
        if (isset($params['homeColumnId']) && $params['homeColumnId'] != '') {
            $andWhere[] = [
                'in',
                'ar.home_column_id',
                StringHelper::changeStrToFilterArr($params['homeColumnId']),
            ];
        }

        // 公告栏目
        if (isset($params['columnId']) && $params['columnId'] != '') {
            $andWhere[] = [
                'in',
                'ac.column_id',
                StringHelper::changeStrToFilterArr($params['columnId']),
            ];
        }

        // 公告属性
        if (isset($params['announcementAttribute']) && $params['announcementAttribute'] != '') {
            $andWhere[] = [
                'in',
                'aa.type',
                StringHelper::changeStrToFilterArr($params['announcementAttribute']),
            ];
        }

        // 招聘状态
        if (isset($params['status']) && $params['status'] != '') {
            $andWhere[] = [
                'in',
                'a.status',
                StringHelper::changeStrToFilterArr($params['status']),
            ];
        }

        // 初发时间
        if (!empty($params['firstReleaseTimeStart']) && !empty($params['firstReleaseTimeEnd'])) {
            $andWhere[] = [
                '>=',
                'ar.first_release_time',
                TimeHelper::dayToBeginTime($params['firstReleaseTimeStart']),
            ];
            $andWhere[] = [
                '<=',
                'ar.first_release_time',
                TimeHelper::dayToEndTime($params['firstReleaseTimeEnd']),
            ];
        }

        // 刷新时间
        if (!empty($params['realRefreshTimeStart']) && !empty($params['realRefreshTimeEnd'])) {
            $andWhere[] = [
                '>=',
                'ar.real_refresh_time',
                TimeHelper::dayToBeginTime($params['realRefreshTimeStart']),
            ];
            $andWhere[] = [
                '<=',
                'ar.first_release_time',
                TimeHelper::dayToEndTime($params['realRefreshTimeEnd']),
            ];
        }

        // 发布时间
        if (!empty($params['refreshTimeStart']) && !empty($params['refreshTimeEnd'])) {
            $andWhere[] = [
                '>=',
                'ar.refresh_time',
                TimeHelper::dayToBeginTime($params['refreshTimeStart']),
            ];
            $andWhere[] = [
                '<=',
                'ar.refresh_time',
                TimeHelper::dayToEndTime($params['refreshTimeEnd']),
            ];
        }

        // 需求专业
        if (isset($params['majorId']) && $params['majorId'] != '') {
            $andWhere[] = [
                'in',
                'jmr.major_id',
                StringHelper::changeStrToFilterArr($params['majorId']),
            ];
        }

        // 学历要求
        if (isset($params['educationType']) && $params['educationType'] != '') {
            $andWhere[] = [
                'in',
                'aer.education_code',
                StringHelper::changeStrToFilterArr($params['educationType']),
            ];
        }

        // 职位类型
        if (isset($params['jobCategoryId']) && $params['jobCategoryId'] != '') {
            $andWhere[] = [
                'in',
                'jcr.category_id',
                StringHelper::changeStrToFilterArr($params['jobCategoryId']),
            ];
        }

        // 工作地点
        if (isset($params['city']) && $params['city'] != '') {
            $andWhere[] = [
                'in',
                'aar.area_id',
                StringHelper::changeStrToFilterArr($params['city']),
            ];
        }

        // 投递方式
        if (isset($params['deliveryWay']) && $params['deliveryWay'] != '') {
            $andWhere[] = [
                'in',
                'a.delivery_way',
                StringHelper::changeStrToFilterArr($params['deliveryWay']),
            ];
        }

        // 投递类型
        if (isset($params['deliveryType']) && $params['deliveryType'] != '') {
            $andWhere[] = [
                'in',
                'a.delivery_type',
                StringHelper::changeStrToFilterArr($params['deliveryType']),
            ];
        }

        // 职位相关查询
        if ((isset($params['establishmentType']) && $params['establishmentType'] != '') || (isset($params['jobName']) && $params['jobName'] != '') || (isset($params['jobCategoryId']) && $params['jobCategoryId'] != '') || (isset($params['majorId']) && $params['majorId'] != '') || (isset($params['educationType']) && $params['educationType'] != '') || (isset($params['department']) && $params['department'] != '') || (isset($params['natureType']) && $params['natureType'] != '') || (isset($params['abroadType']) && $params['abroadType'] != '') || (isset($params['experienceType']) && $params['experienceType'] != '') || (isset($params['titleType']) && $params['titleType'] != '') || (isset($params['politicalType']) && $params['politicalType'] != '') || (isset($params['city']) && $params['city'] != '')) {
            $jobQuery = BaseJob::find()
                ->alias('j');
            if (isset($params['jobCategoryId']) && $params['jobCategoryId'] != '') {
                $jobQuery->leftJoin(['jcr' => BaseJobCategoryRelation::tableName()], 'jcr.job_id = j.id');
            }
            if (isset($params['majorId']) && $params['majorId'] != '') {
                $jobQuery->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'jmr.job_id = j.id');
            }

            $jobAndWhere = [
                'and',
                [
                    '<>',
                    'j.announcement_id',
                    0,
                ],
            ];

            // 编制类型
            if ($params['establishmentType']) {
                // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
                $establishmentType = explode(',', $params['establishmentType']);
                $orWhere           = [
                    'or',
                ];
                foreach ($establishmentType as $itemKey => $establishmentItem) {
                    if ($establishmentItem == -1) {
                        $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
                    } else {
                        $establishmentItemKey = 'establishmentItem' . $itemKey;
                        $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
                            [$establishmentItemKey => $establishmentItem]);
                    }
                }
                $jobAndWhere[] = $orWhere;
            }

            // 职位检索
            if (isset($params['jobName']) && $params['jobName'] != '') {
                $jobAndWhere[] = [
                    'or',
                    [
                        'like',
                        'j.name',
                        $params['jobName'],
                    ],
                    [
                        'like',
                        'j.uuid',
                        $params['jobName'],
                    ],
                ];
            }

            // 职位类型
            if (isset($params['jobCategoryId']) && $params['jobCategoryId'] != '') {
                $jobAndWhere[] = [
                    'in',
                    'jcr.category_id',
                    $params['jobCategoryId'],
                ];
            }

            // 学历要求
            if ((isset($params['educationType']) && $params['educationType'] != '')) {
                $jobAndWhere[] = [
                    'in',
                    'j.education_type',
                    StringHelper::changeStrToFilterArr($params['educationType']),
                ];
            }

            // 需求学科
            if (isset($params['majorId']) && $params['majorId'] != '') {
                $jobAndWhere[] = [
                    'in',
                    'jmr.major_id',
                    StringHelper::changeStrToFilterArr($params['majorId']),
                ];
            }

            // 工作城市
            if (isset($params['city']) && $params['city'] != '') {
                $jobAndWhere[] = [
                    'in',
                    'j.city_id',
                    StringHelper::changeStrToFilterArr($params['city']),
                ];
            }

            // 用人部门
            if (isset($params['department']) && $params['department'] != '') {
                $jobAndWhere[] = [
                    'like',
                    'j.department',
                    $params['department'],
                ];
            }

            // 工作性质
            if (isset($params['natureType']) && $params['natureType'] != '') {
                $jobAndWhere[] = [
                    'in',
                    'j.nature_type',
                    StringHelper::changeStrToFilterArr($params['natureType']),
                ];
            }

            // 海外经历
            if (isset($params['abroadType']) && $params['abroadType'] != '') {
                $jobAndWhere[] = [
                    'in',
                    'j.abroad_type',
                    StringHelper::changeStrToFilterArr($params['abroadType']),
                ];
            }
            // 工作经历
            if (isset($params['experienceType']) && $params['experienceType'] != '') {
                $jobAndWhere[] = [
                    'in',
                    'j.experience_type',
                    StringHelper::changeStrToFilterArr($params['experienceType']),
                ];
            }

            // 职称要求title_type
            if (isset($params['titleType']) && $params['titleType'] != '') {
                $jobAndWhere[] = [
                    'in',
                    'j.title_type',
                    StringHelper::changeStrToFilterArr($params['titleType']),
                ];
            }

            // 政治面貌
            if (isset($params['politicalType']) && $params['politicalType'] != '') {
                $jobAndWhere[] = [
                    'in',
                    'j.political_type',
                    StringHelper::changeStrToFilterArr($params['politicalType']),
                ];
            }

            $jobAnnouncementIds = $jobQuery->select(['j.announcement_id'])
                ->where($jobAndWhere)
                ->orderBy('j.id desc')
                ->groupBy('j.announcement_id')
                ->limit(100)
                ->column();

            DebugHelper::writeLog($jobQuery->createCommand()
                ->getRawSql(), '公告列表查询时涉及到的职位sql');
            if (empty($jobAnnouncementIds)) {
                $jobAnnouncementIds = [0];
            }

            $andWhere[] = [
                'in',
                'a.id',
                $jobAnnouncementIds,
            ];
        }

        // 审核人
        if (isset($params['auditAdminName']) && $params['auditAdminName'] != '') {
            $andWhere[] = [
                'like',
                'a.audit_admin_name',
                $params['auditAdminName'],
            ];
        }

        // 审核状态
        if (isset($params['auditStatus']) && $params['auditStatus'] != '') {
            $andWhere[] = [
                'in',
                'a.audit_status',
                StringHelper::changeStrToFilterArr($params['auditStatus']),
            ];
        }

        // 是否小程序
        if (isset($params['isMiniapp']) && $params['isMiniapp'] != '') {
            $andWhere[] = [
                'in',
                'a.is_miniapp',
                StringHelper::changeStrToFilterArr($params['isMiniapp']),
            ];
        }

        // 单位类型
        if (isset($params['companyType']) && $params['companyType'] != '') {
            $andWhere[] = [
                'in',
                'c.type',
                StringHelper::changeStrToFilterArr($params['companyType']),
            ];
        }

        // 单位性质
        if (isset($params['companyNature']) && $params['companyNature'] != '') {
            $andWhere[] = [
                'in',
                'c.nature',
                StringHelper::changeStrToFilterArr($params['companyNature']),
            ];
        }

        // 显示状态
        if (isset($params['isShow']) && $params['isShow'] != '') {
            $andWhere[] = [
                'in',
                'ar.is_show',
                StringHelper::changeStrToFilterArr($params['isShow']),
            ];
        }

        // 申请人
        if (isset($params['applyAdminName']) && $params['applyAdminName'] != '') {
            $andWhere[] = [
                'like',
                'a.apply_admin_name',
                $params['applyAdminName'],
            ];
        }

        return $query->andWhere($andWhere);
    }

    /**
     * 批量生成操作按钮列表
     *
     * @param  $buttonKeys 所需的按钮组
     * @param  $jobData    职位数据
     * @return array 过滤后的按钮列表
     */
    public function generateButtonList(
        $announcementData = [],
        $buttonKeys = []
    ) {
        $btnList = [];
        foreach ($buttonKeys as $buttonKey) {
            $button = (new OperateService())->run($buttonKey, $announcementData['is_show'], $announcementData['status'],
                $announcementData['audit_status']);

            // 修正：使用当前按钮变量名
            if ($button['disabled'] != 3) {
                $btnList[] = $button;
            }
        }

        return $btnList;
    }
}