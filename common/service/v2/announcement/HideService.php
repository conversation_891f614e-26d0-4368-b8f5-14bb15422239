<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseAnnouncementLog;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseJob;
use common\components\MessageException;
use common\helpers\IpHelper;
use common\helpers\StringHelper;

/**
 * 公告显示&隐藏
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class HideService extends BaseService
{
    private $handleLogData;

    public function __construct()
    {
        parent::__construct();
    }

    public function hideBatch($params)
    {
        $successIds  = [];
        $failMessage = [];
        $this->initInfo();
        $ids = StringHelper::changeStrToFilterArr($params['announcementId'] ?? '');
        foreach ($ids as $id) {
            try {
                $this->params['announcementId'] = $id;
                $this->setAnnouncement($this->params['announcementId'])
                    ->validate()
                    ->beforeHandleLog()
                    ->run()
                    ->handleLog()
                    ->log(BaseAnnouncementLog::TYPE_OFFLINE_BATCH)
                    ->afterAnnouncementUpdateJob();

                if ($this->announcementId) {
                    $successIds[] = $this->announcementId;
                }

            } catch (MessageException $exception) {
                $failMessage[] = '公告ID：' . $id . '，再发布失败，原因：' . $exception->getMessage();
            }
        }

        return [
            'successIds'  => $successIds,
            'failMessage' => implode('<br>', $failMessage),
        ];
    }

    public function hide($params)
    {
        $this->params = $params;
        $this->initInfo();
        $this->setAnnouncement($this->params['announcementId'])
            ->validate()
            ->beforeHandleLog()
            ->run()
            ->handleLog()
            ->log(BaseAnnouncementLog::TYPE_OFFLINE)
            ->afterAnnouncementUpdateJob();
    }

    /**
     * 数据验证
     */
    public function validate()
    {
        if (empty($this->params['announcementId'])) {
            throw new \Exception('公告ID不能为空');
        }

        $operateRes = (new OperateService())->run('hide', $this->articleInfo->is_show, $this->announcementInfo->status,
            $this->announcementInfo->audit_status);
        if ($operateRes['disabled'] != 1) {
            throw new MessageException('公告状态不允许隐藏');
        }

        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $this->baseCheckMemberPackageRefresh($this->announcementInfo->company_id);
        }

        return $this;
    }

    public function run()
    {
        $this->articleInfo->is_show = BaseArticle::IS_SHOW_NO;
        $this->articleInfo->status  = BaseArticle::STATUS_OFFLINE;

        if (!$this->articleInfo->save()) {
            throw new \Exception($this->articleInfo->getFirstErrorsMessage());
        }

        // 删除公告文档属性
        BaseArticleAttribute::deleteAttribute($this->articleInfo->id);

        $jobIds = BaseJob::find()
            ->select(['id'])
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => BaseJob::STATUS_ONLINE,
            ])
            ->column();

        if ($jobIds) {
            $jobIds            = implode(',', $jobIds);
            $jobOfflineService = new \common\service\v2\job\IsShowService();
            $jobOfflineService->isShowBatch([
                'jobIds' => $jobIds,
                'isShow' => BaseJob::IS_SHOW_NO,
            ]);
        }

        return $this;
    }

    private function beforeHandleLog()
    {
        // 记录一下log数据
        $this->handleLogData = [
            'add_time'        => CUR_DATETIME,
            'job_id'          => 0,
            'handle_type'     => strval(BaseAnnouncementHandleLog::HANDLE_TYPE_HIDDEN),
            'handler_type'    => strval($this->params['platformType']),
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '公告显示状态' => BaseArticle::IS_SHOW_LIST[$this->articleInfo->is_show],
                '更新时间'     => $this->announcementInfo->update_time,
            ], JSON_UNESCAPED_UNICODE),
            'handle_after'    => '',
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->announcementInfo->id,
        ];

        return $this;
    }

    /**
     * 在执行后
     * @throws \yii\base\NotSupportedException
     */
    public function handleLog()
    {
        $this->handleLogData['handle_after'] = json_encode([
            '公告显示状态' => BaseArticle::IS_SHOW_LIST[$this->articleInfo->is_show],
            '更新时间' => CUR_DATETIME,
        ], JSON_UNESCAPED_UNICODE);
        // 消耗套餐,写日志等等
        BaseAnnouncementHandleLog::createInfo($this->handleLogData);

        return $this;
    }
}