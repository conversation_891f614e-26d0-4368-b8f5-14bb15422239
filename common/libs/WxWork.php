<?php

namespace common\libs;

use common\base\models\BaseAdmin;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseVerificationCodeLog;
use common\base\models\BaseWxWorkRobotConfig;
use common\helpers\IpHelper;
use common\models\Admin;
use common\models\WxWorkRobotLog;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\File;
use EasyWeChat\Kernel\Messages\Media;
use EasyWeChat\Kernel\Messages\TaskCard;
use EasyWeChat\Kernel\Messages\TextCard;
use EasyWeChat\Work\GroupRobot\Messages\Markdown;
use GuzzleHttp\Client;
use Yii;
use yii\base\Exception;

class WxWork
{
    // 初始化
    private static $instance = null;
    private static $app;

    // 简历相关
    const PERMISSIONS_RESUME = 1;
    // 投递相关
    const PERMISSIONS_JOB_APPLY = 2;
    // 双会相关
    const PERMISSIONS_DOUBLE_MEETING = 3;
    // 我的客户权限
    const PERMISSIONS_MY_COMPANY_LIST = 4;
    // 订单相关
    const PERMISSIONS_ORDER = 5;

    const VERIFICATION_CODE_TASK_BASE_KEY    = 'verificationCodeTask';
    const VERIFICATION_CODE_TASK_CONFIRM_KEY = 'verificationCodeTaskConfirm';
    const VERIFICATION_CODE_TASK_CANCEL_KEY  = 'verificationCodeTaskCancel';

    public function __construct()
    {
        if (!Yii::$app->params['wxWork']) {
            return;
        }

        self::$app = Factory::work(Yii::$app->params['wxWork']);
    }

    public static function getInstance(): WxWork
    {
        if (empty(self::$instance)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    public function getAllUser()
    {
        $key = Cache::WX_WORK_ALL_USER_LIST_KEY;

        $userList = Cache::get($key);

        if ($userList) {
            return json_decode($userList, true);
        }

        $departmentList = self::$app->department->list()['department'];
        // 拿部门id作为key
        $departmentList = array_combine(array_column($departmentList, 'id'), $departmentList);

        $list       = [];
        $userIdList = [];

        foreach ($departmentList as $department) {
            $user = self::$app->user->getDetailedDepartmentUsers($department['id'], true);
            foreach ($user['userlist'] as $item) {
                $departmentName         = $departmentList[$item['main_department']]['name'];
                $item['departmentName'] = $departmentName;
                if (!in_array($item['userid'], $userIdList)) {
                    $list[]       = $item;
                    $userIdList[] = $item['userid'];
                }
            }
        }

        Cache::set($key, json_encode($list), 7200);

        return $list;
    }

    /**
     *
     * 发送信息到网站运营部审核组
     * @param $content
     * @return void
     * @throws Exception
     */
    public function messageToWebsiteOperationAudit($content)
    {
        $system = Yii::$app->params['wxWorkContact']['websiteOperationAudit'];
        if (!$system) {
            return;
        }
        foreach ($system as $item) {
            $this->message($item, $content);
        }
    }

    /**
     * 通知系统级别的人
     * @param $adminId
     * @param $content
     * @return void
     */
    public function messageToSystem($content)
    {
        $system = Yii::$app->params['wxWorkContact']['system'];
        if (!$system) {
            return;
        }
        foreach ($system as $item) {
            $this->message($item, $content);
        }
    }

    /**
     * 通知系统级别的人
     * @param $adminId
     * @param $content
     * @return void
     */
    public function messageToOperation($content)
    {
        $system = Yii::$app->params['wxWorkContact']['operation'];
        if (!$system) {
            return;
        }
        foreach ($system as $item) {
            $this->message($item, $content);
        }
    }

    public function cardToOperation($title, $description, $url = '')
    {
        $system = Yii::$app->params['wxWorkContact']['operation'];
        if (!$system) {
            return;
        }
        foreach ($system as $item) {
            $this->card($item, $title, $description, $url);
        }
    }

    public function downLoadMessage($adminId, $url, $title = '')
    {
        $userid = Admin::findOneVal(['id' => $adminId], 'wx_work_userid');
        if (!$userid) {
            throw new Exception('没有绑定微信号');
        }
        $messenger = self::$app->messenger;
        //
        $message = new TextCard([
            'title'       => $title,
            'description' => '你在' . CUR_DATETIME . '下载了一个文件,点击打开',
            'url'         => $url,
        ]);
        $messenger->message($message)
            ->toUser($userid)
            ->send();
    }

    public function message($adminId, $content)
    {
        $userid = Admin::findOneVal(['id' => $adminId], 'wx_work_userid');
        if (!$userid) {
            //throw new Exception('没有绑定微信号');
            return;
        }
        $messenger = self::$app->messenger;
        $messenger->message($content)
            ->toUser($userid)
            ->send();
    }

    public function markdown($adminId, $content)
    {
        $userid = Admin::findOneVal(['id' => $adminId], 'wx_work_userid');
        if (!$userid) {
            throw new Exception('没有绑定微信号');
        }
        $messenger = self::$app->messenger;
        $markdown  = new Markdown($content);
        $messenger->message($markdown)
            ->toUser($userid)
            ->send();
    }

    public function card($adminId, $title, $description, $url = '')
    {
        $userid = Admin::findOneVal(['id' => $adminId], 'wx_work_userid');
        if (!$userid) {
            throw new Exception('没有绑定微信号');
        }
        $messenger = self::$app->messenger;
        //
        $message = new TextCard([
            'title'       => $title,
            'description' => $description,
            'url'         => $url,
        ]);
        $messenger->message($message)
            ->toUser($userid)
            ->send();
    }

    public function buttonCard($adminId, $title, $description, $url = '', $button = '')
    {
        $userid = Admin::findOneVal(['id' => $adminId], 'wx_work_userid');
        if (!$userid) {
            throw new Exception('没有绑定微信号');
        }
        $messenger = self::$app->messenger;
        //
        $message = new TaskCard([
            'title'       => $title,
            'description' => $description,
            'url'         => $url,
            'task_id'     => CUR_TIMESTAMP,
            'btn'         => [
                [
                    'name' => '测试2',
                    'key'  => 'key1',
                ],
                [
                    'name' => '测试1',
                    'key'  => 'key2',
                ],
            ],
            // 'btn'         => [
            //     'name' => '测试1',
            //     'key'  => 'key2',
            // ],
        ]);
        $rs      = $messenger->message($message)
            ->toUser($userid)
            ->send();

        bb($rs);
    }

    public function createMenu($menus)
    {
        return self::$app->menu->create($menus);
    }

    public function getMenu()
    {
        try {
            return self::$app->menu->get();
        } catch (Exception $e) {
            bb($e);
        }
    }

    public function checkPermissions($adminId, $type)
    {
        if (!$adminId) {
            return false;
        }

        // 这里处理特殊权限
        if ($type == self::PERMISSIONS_MY_COMPANY_LIST) {
            // 看一下是否已经有进行的的这个任务,以免太多
            if (BaseAdminDownloadTask::find()
                ->where([
                    'admin_id' => $adminId,
                    'type'     => BaseAdminDownloadTask::TYPE_MY_COMPANY_LIST,
                    'status'   => [
                        BaseAdminDownloadTask::STATUS_WAIT,
                        BaseAdminDownloadTask::STATUS_IN_EXECUTE,
                    ],
                ])
                ->exists()) {
                return false;
            }
        }

        // 只要是研发部都有权限
        $departmentId = BaseAdmin::findOneVal(['id' => $adminId], 'department_id');
        if ($departmentId == 17) {
            return true;
        }

        $config = Yii::$app->params['wxWorkContact']['permissions'];
        if (!$config) {
            return false;
        }

        switch ($type) {
            case self::PERMISSIONS_RESUME:
                if (in_array($adminId, $config['resume'])) {
                    return true;
                }
                break;
            case self::PERMISSIONS_JOB_APPLY:
                if (in_array($adminId, $config['jobApply'])) {
                    return true;
                }
                break;
            case self::PERMISSIONS_DOUBLE_MEETING:
                if (in_array($adminId, $config['doubleMeeting'])) {
                    return true;
                }
                break;
            case self::PERMISSIONS_ORDER:
                if (in_array($adminId, $config['order'])) {
                    return true;
                }
                break;
            case self::PERMISSIONS_MY_COMPANY_LIST:
                // 这里理论上只有销售有权限
                if (BaseAdmin::checkSale($adminId)) {
                    return true;
                }

                break;
        }

        return false;
    }

    // 这里开始是机器人部分
    public function robotMessageToOperation($content, $isRichText = false)
    {
        $operationKey = $this->getTokenByKey('operation');
        if (!$operationKey) {
            return;
        }

        if ($isRichText) {
            $markdown = new Markdown($content);
            $content  = $markdown;
        }

        $this->robotMessage($operationKey, $content);
    }

    // 这里开始是机器人部分
    public function robotMessageToSystem($content, $isRichText = false)
    {
        $operationKey = $this->getTokenByKey('system');
        if (!$operationKey) {
            return;
        }

        if ($isRichText) {
            $markdown = new Markdown($content);
            $content  = $markdown;
        }

        $this->robotMessage($operationKey, $content);
    }

    // 这里开始是机器人部分
    public function robotMessageToSms($content, $isRichText = false)
    {
        $operationKey = $this->getTokenByKey('sms');
        if (!$operationKey) {
            return;
        }

        if ($isRichText) {
            $markdown = new Markdown($content);
            $content  = $markdown;
        }

        $this->robotMessage($operationKey, $content);
    }

    // 这里开始是机器人部分
    public function robotMessageToPaySuccess($content, $isRichText = false)
    {
        $operationKey = $this->getTokenByKey('paySuccess');
        if (!$operationKey) {
            return;
        }

        if ($isRichText) {
            $markdown = new Markdown($content);
            $content  = $markdown;
        }

        $this->robotMessage($operationKey, $content);
    }

    /**
     * PDF版本转换失败提醒
     * @param $content
     * @param $isRichText
     * @return void
     */
    public function robotMessageToPdfError($content, $isRichText = false)
    {
        $operationKey = $this->getTokenByKey('pdfErrorNotice');
        if (!$operationKey) {
            return;
        }

        if ($isRichText) {
            $markdown = new Markdown($content);
            $content  = $markdown;
        }

        $this->robotMessage($operationKey, $content);
    }

    // 运营人才套餐配置设置
    public function robotMessageToAdminPackageSetting($content, $isRichText = false)
    {
        $operationKey = $this->getTokenByKey('adminPackageSetting');
        if (!$operationKey) {
            return;
        }

        if ($isRichText) {
            $markdown = new Markdown($content);
            $content  = $markdown;
        }

        $this->robotMessage($operationKey, $content);
    }

    //推送单位高才通权益咨询消息
    public function robotMessageToCompanyEquityConsultNotice($content, $isRichText = false)
    {
        $operationKey = $this->getTokenByKey('companyEquityConsultNotice');
        if (!$operationKey) {
            return;
        }

        if ($isRichText) {
            $markdown = new Markdown($content);
            $content  = $markdown;
        }

        $this->robotMessage($operationKey, $content);
    }

    // 爬虫监控
    public function robotMessageToCrawler($content, $isRichText = false)
    {
        $operationKey = $this->getTokenByKey('crawler');
        if (!$operationKey) {
            return;
        }

        if ($isRichText) {
            $markdown = new Markdown($content);
            $content  = $markdown;
        }

        $this->robotMessage($operationKey, $content);
    }

    public function robotDataDaily($content, $params = [])
    {
        $operationKey = $this->getTokenByKey('dataDaily');
        if (!$operationKey) {
            return;
        }

        if ($params) {
            $type = $params['type'] ?? '';
            if ($type === 'markdown') {
                $markdown = new Markdown($content);
                $content  = $markdown;
            }
            if ($type === 'card') {
                $message = new TextCard([
                    'title'       => $params['title'],
                    'description' => $content,
                    'url'         => $params['url'],
                ]);
                $content = $message;
            }
        }

        $this->robotMessage($operationKey, $content);
    }

    /**
     * 业务推送消息-采用卡片样式
     * @param $content
     * @return void
     */
    public function robotBusinessPushMessage($content)
    {
        $operationKey = $this->getTokenByKey('businessPush');
        if (!$operationKey) {
            return;
        }
        $testText = "
        ### {$content['title']}
        {$content['description']}  <font color='blue'>[点击下载]({$content['url']})</font>";
        $markdown = new Markdown($testText);

        $this->robotMessage($operationKey, $markdown);
    }

    /**
     * 业务推送单位操作公告职位消息-采用卡片样式
     * @param $content
     * @return void
     */
    public function robotPushCompanyEditMessage($content)
    {
        $operationKey = $this->getTokenByKey('pushCompanyEditMessage');
        if (!$operationKey) {
            return;
        }
        $ip         = IpHelper::getIp();
        $statusText = isset($content['beforeStatusText']) ? '状态：由' . $content['beforeStatusText'] . '变为' . $content['afterStatusText'] : '';
        if ($statusText) {
            $testText = <<<MD
### {$content['title']}
    单位名称：{$content['companyName']}
    单位ID：{$content['companyUuid']}
    IP：{$ip}
    {$statusText}
    {$content['content']}
MD;
        } else {
            $testText = <<<MD
### {$content['title']}
    单位名称：{$content['companyName']}
    单位ID：{$content['companyUuid']}
    IP：{$ip}
    {$content['content']}
MD;
        }

        $markdown = new Markdown($testText);
        $this->robotMessage($operationKey, $markdown);
    }

    /**
     * 业务推送消息-采用卡片样式
     * --公告年度运营数据
     * @param $content
     * @return void
     */
    public function robotBusinessPushMessageAnnouncement($content)
    {
        $operationKey = $this->getTokenByKey('businessPushAnnouncement');
        if (!$operationKey) {
            return;
        }
        $testText = "
                ### {$content['title']}
                {$content['description']}   <font color='blue'>[点击下载]({$content['url']})</font>";
        $markdown = new Markdown($testText);

        $this->robotMessage($operationKey, $markdown);
    }

    /**
     * 业务推送消息
     * 推送活动管理的消息
     * @param $content
     * @return void
     */
    public function robotActivityPushMessage($content)
    {
        $operationKey = Yii::$app->params['wxWorkRobot']['activityPushMessage']['key'];
        if (!$operationKey) {
            return;
        }

        $this->robotMessage($operationKey, $content);
    }

    public function robotJobApplyWarring($content)
    {
        $operationKey = $this->getTokenByKey('jobApplyWarn');
        if (!$operationKey) {
            return;
        }

        $this->robotMessage($operationKey, $content);
    }

    public function robotMessage($key, $content)
    {
        $model             = new WxWorkRobotLog();
        $wxWorkRobotConfig = BaseWxWorkRobotConfig::findOne(['token' => $key]);
        if (!$wxWorkRobotConfig) {
            Yii::error('没有找到对应的机器人');

            return true;
        }
        $model->robot_id = $wxWorkRobotConfig->id;
        $model->token    = $wxWorkRobotConfig->token;
        if (is_object($content)) {
            $model->msg_content = $content->content;
        } else {
            $model->msg_content = $content ?: '';
        }

        try {
            // 非正式环境*（添加环境变量）
            // 获取环境
            $env = Yii::$app->params['environment'];

            // 必须是正式环境才需要
            if ($env !== 'prod') {
                if (!is_object($content)) {
                    $content->content = $env . ' : ' . $content->content;
                }
            }

            $model->status = 1;

            $messenger = self::$app->group_robot_messenger;
            $rs        = $messenger->toGroup($key)
                ->send($content);

            $model->response = json_encode($rs);
        } catch (\Exception $e) {
            $model->status   = 1;
            $model->response = $e->getMessage();

            Yii::error($e->getMessage());
        }

        if (!$model->save()) {
            Yii::error($model->getFirstErrorsMessage());
        }
    }

    public function sendVerificationCodeLogConfirm($adminId, $logId, $content)
    {
        $userid = Admin::findOneVal(['id' => $adminId], 'wx_work_userid');
        if (!$userid) {
            throw new Exception('没有绑定微信号');
        }

        // 发送确认按钮给用户
        $message = new TaskCard([
            'title'       => '请确认是否获取验证码',
            'description' => $content,
            'task_id'     => self::VERIFICATION_CODE_TASK_BASE_KEY . '_' . $logId,
            'btn'         => [
                [
                    'name'  => '确认',
                    'key'   => self::VERIFICATION_CODE_TASK_CONFIRM_KEY,
                    // 蓝色按钮
                    'color' => 'blue',
                ],
                [
                    'name' => '取消',
                    'key'  => self::VERIFICATION_CODE_TASK_CANCEL_KEY,
                    // 灰色按钮
                    'type' => 'default',

                ],
            ],
        ]);

        $messenger = self::$app->messenger;

        $rs = $messenger->message($message)
            ->toUser($userid)
            ->send();

        if ($rs['errcode'] != 0) {
            throw new Exception('发送失败');
        }

        return true;
    }

    /**
     * @param $message
     *
     * 'MsgType' => 'event',
     * 'Event' => 'taskcard_click',
     * 'CreateTime' => '1736237891',
     * 'EventKey' => 'verificationCodeTaskConfirm',
     * 'TaskId' => 'verificationCodeTask7',
     */
    public function verifyClick($message, $adminId)
    {
        $taskId = $message['TaskId'];
        if (!$taskId) {
            return false;
        }

        // 分割出来任务和相对于的主id
        $taskArray = explode('_', $taskId);
        if (count($taskArray) != 2) {
            return false;
        }

        $key   = $taskArray[0];
        $logId = $taskArray[1];

        switch ($key) {
            case self::VERIFICATION_CODE_TASK_BASE_KEY:
                // 获取验证码
                if ($message['EventKey'] == self::VERIFICATION_CODE_TASK_CANCEL_KEY) {
                    // 取消
                    $rs = BaseVerificationCodeLog::submitCancel($logId, $adminId);
                    if ($rs) {
                        return '取消获取验证码成功';
                    }
                }

                if ($message['EventKey'] == self::VERIFICATION_CODE_TASK_CONFIRM_KEY) {
                    // 确认
                    $code = BaseVerificationCodeLog::submitConfirm($logId, $adminId);

                    if ($code) {
                        // 系统通知
                        $adminName = Admin::findOneVal(['id' => $adminId], 'name');
                        $this->robotMessageToSystem('用户' . $adminName . '已经确认获取验证码成功{id:' . $logId . '}');

                        $this->message($adminId, $code);

                        return $code;
                    }

                    return '获取验证码失败';
                }
        }

        return false;
    }

    public function getTokenByKey($key)
    {
        return BaseWxWorkRobotConfig::findOneVal(['key' => $key], 'token');
    }
}
