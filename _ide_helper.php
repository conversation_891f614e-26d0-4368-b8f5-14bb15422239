<?php

class Yii
{
    /**
     * @var MyApplication
     */
    public static $app;
}

/**
 * @property \common\base\models\BaseMember      $member
 * @property \yii\queue\redis\Queue              $smsQueue
 * @property \yii\queue\redis\Queue              $emailQueue
 * @property \yii\queue\redis\Queue              $emailQueue2
 * @property \yii\queue\redis\Queue              $emailQueue3
 * @property \yii\queue\redis\Queue              $emailQueue4
 * @property \yii\queue\redis\Queue              $emailQueue5
 * @property \yii\queue\redis\Queue              $showcaseBrowseLogQueue
 * @property \yii\queue\redis\Queue              $clickQueue
 * @property \yii\queue\redis\Queue              $memberSearchQueue
 * @property \yii\queue\redis\Queue              $adminDownloadTaskQueue
 * @property \yii\queue\redis\Queue              $adminDownloadTaskNewQueue
 * @property \yii\queue\redis\Queue              $engineUpdateStatisticsTableQueue
 * @property \yii\queue\redis\Queue              $personResumeIntentionMatchJobQueue
 * @property \yii\queue\redis\Queue              $companyJobMatchPersonQueue
 * @property \yii\queue\redis\Queue              $afterAnnouncementUpdateJobQueue
 * @property \yii\queue\redis\Queue              $companyResumePvTotalQueue
 * @property zhuzixian520\meilisearch\Connection $meilisearch
 * @property \yii\queue\redis\Queue              $redisUpdateQueue
 * @property \yii\queue\redis\Queue              $companyGroupScoreSystemQueue
 * @property \yii\queue\redis\Queue              $hwActivityCompanyQueue
 */
class MyApplication
{
}
