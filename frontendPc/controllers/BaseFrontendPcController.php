<?php

namespace frontendPc\controllers;

use common\base\BaseController;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseSeoUserAgent;
use common\helpers\DebugHelper;
use common\helpers\IpHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use frontendPc\models\HomeColumn;
use UserAgentParser\Provider\WhichBrowser;
use Yii;
use yii\base\Action;

class BaseFrontendPcController extends BaseController
{
    public $pcToH5ActionList = [
        // 栏目页,公告详情,,企业列表,,,每日汇总详情,资讯详情
        // 首页
        'home/index',
        // 职位列表
        'job/index',
        'job/detail',
        // 职位详情
        'job/detail',
        // 单位详情
        'company/detail',
        // 单位公告列表
        'company/detail-announcement-list',
        // 单位职位列表
        'company/detail-job-list',
        //活动
        'company/detail-activity-list',
        // 单位列表
        'company/index',
        // 资讯详情
        'news/detail',
        // 每日汇总详情
        'home/daily-summary-detail',
        // 每日公告汇总详情
        'daily-announcement-summary/detail',
        // 公告详情
        'announcement/detail',
        // 公告预览
        'announcement/preview',
        // 栏目页
        'home/column',
        // 旧文章页
        'home/old-article',
        // 公告详情
        'announcement/detail',
        // 旧栏目页
        'home/old-column',
        // 专业页
        'home/major',
        // 每日汇总详情
        'home/daily',
        // 地区页
        'home/region',
        // 短链接
        'short-link/index',
        // 底部页
        'home/footer',
        // 职位c页面
        'engine/index',
        // 热门词
        'hot-word/index',
        // 公告预览
        'announcement/preview',
        // 活动页（活动报名页）
        'activity/index',
        // 活动报名页
        'activity-for-registration-form/index',
        // 介绍页（vip)
        'introduction-page/vip',
        // 介绍页（vip)
        'introduction-page/competition',
        // 介绍页（vip)
        'introduction-page/activity-vip',
        // 介绍页（vip)
        'introduction-page/job-fast',
        // 职位热度
        'job/report',
        // 公告热度
        'announcement/report',
        // 百科关键字
        'seo-job-wiki/index'
    ];

    public function isResumeLogin()
    {
        $memberId = Yii::$app->user->id;
        if (!$memberId) {
            return false;
        }

        $type = Yii::$app->params['user']['type'];

        if ($type != BaseMember::TYPE_PERSON) {
            return false;
        }

        return $memberId;
    }

    public function beforeAction($action)
    {
        //iframe允许同源嵌套 不同源就不允许
        header("X-Frame-Options: SAMEORIGIN");
        //对蜘蛛行为进行处理
        if (!BaseSeoUserAgent::isAllowAccess()) {
            BaseSeoUserAgent::redirect503();
        }
        // 限制域名指定的域名访问
        if (!BaseSeoUserAgent::isAllowHost()) {
            BaseSeoUserAgent::redirect503();
        }
        if (!parent::beforeAction($action)) {
            return false;
        }

        $this->check($action);

        // 判断是否ajax请求
        if (!Yii::$app->request->isAjax) {
            $this->setSeo();
            $this->toH5($action);
        }

        if (!Yii::$app->user->isGuest) {
            $model = new BaseMemberLoginForm();
            $model->setMemberInfo(true);
            $user                     = BaseMember::getLoginInfo();
            Yii::$app->params['user'] = $user;
            $this->setActive();
        }

        $this->logAll();

        return parent::beforeAction($action);
    }

    /**
     * 无需登录就可以操作的控制器
     * @return string[]
     */
    // public function ignoreLogin()
    // {
    //     return [
    //         'company/index',
    //         'person/index',
    //         'company/test',
    //         'home/index',
    //         'home/validate',
    //         'home/get-captcha-config',
    //         'test/index',
    //     ];
    // }

    /**
     * title
     * keywords
     * description
     * Copyright
     *
     * @param $data
     */
    public function setSeo($data = [])
    {
        // 避免有些页面没有
        $config = Yii::$app->params['seo']['default'];

        // 特殊设置,找到当前的控制器
        $actionId = Yii::$app->controller->action->uniqueId;

        $pcConfig = Yii::$app->params['seo']['pc']['specialRoute'];

        if ($pcConfig && isset($pcConfig[$actionId])) {
            $config = array_merge($config, $pcConfig[$actionId]);
        }

        $this->getView()->title = $data['title'] ?: $config["title"];

        $keywordsFlag = 'keywords'; //关键点是这个标记！！！
        $this->view->registerMetaTag([
            'name'    => 'keywords',
            'content' => $data['keywords'] ?: $config["keywords"],
        ], $keywordsFlag);

        $descFlag = 'desc'; //关键点也是这个标记！！！
        $this->view->registerMetaTag([
            'name'    => 'description',
            'content' => $data['description'] ?: $config["description"],
        ], $descFlag);

        $descFlag = 'Copyright'; //关键点也是这个标记！！！
        $this->view->registerMetaTag([
            'name'    => 'Copyright',
            'content' => $config["Copyright"],
        ], $descFlag);
    }

    public function notFound()
    {
        Yii::$app->response->setStatusCode(404)
            ->send();
        echo $this->renderPartial('/home/<USER>');
        exit();
    }

    // 没有权限
    public function notPermissions()
    {
        Yii::$app->response->setStatusCode(403)
            ->send();
        exit();
    }

    /**
     * @param Action $action
     * @return true
     */
    public function check(Action $action)
    {
        $ua = Yii::$app->request->getUserAgent();
        // 来源
        $referer = Yii::$app->request->getReferrer();

        // ip

        // 游客才限制
        if (Yii::$app->user->isGuest) {
            if ($referer == 'https://google.com') {
                $this->notPermissions();
            }

            if (in_array($action->uniqueId, [
                'job/index',
                'company/index',
            ])) {
                $banUa = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 6P Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2526.83 Mobile Safari/537.36; 360Spider',
                    'Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
                    'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
                    'Mozilla/5.0 (compatible; ChatGLM-Spider/1.0; +https://chatglm.cn/)'
                ];

                if (in_array($ua, $banUa)) {
                    return $this->redirect('/member/person/login');
                    $this->notFound();
                }
            }

            $ip = IpHelper::getIp();

            // 如果ip是14.155.开头，先让去登录页
            if (strpos($ip, '14.155.') === 0) {
                return $this->redirect('/member/person/login');
                $this->notFound();
            }

            if ($ua == 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/116.0.1938.76') {
                if (in_array($action->uniqueId, [
                    'home/change-second-column-announcement-list-account',
                ])) {
                    $this->notFound();
                }
            }
        }

        return true;
    }

    /**
     * 从pc跳转到h5页面
     */
    public function toH5(Action $action)
    {
        $ua = Yii::$app->request->headers['user-agent'];
        try {
            $result = (new WhichBrowser())->parse($ua);
            if ($result->isMobile() && in_array($action->uniqueId, $this->pcToH5ActionList)) {
                if ($action->uniqueId == 'home/old-article') {
                    // 旧普页特殊处理
                    $id        = Yii::$app->request->get('id');
                    $h5Host    = Yii::$app->params['h5Host'];
                    $h5FullUrl = 'http://' . $h5Host . '/view.php?aid=' . $id;
                    header("Location: $h5FullUrl");

                    exit;
                }

                if ($action->uniqueId == 'home/old-column') {
                    // 旧栏目页特殊处理
                    $level1 = Yii::$app->request->get('level1');
                    $level2 = Yii::$app->request->get('level2');

                    // 这里会有一些特殊页面的输出
                    if ($level1 == 'diqu' && !$level2) {
                        return $this->actionRegion();
                    }
                    $path = $level2 ? $level1 . '/' . $level2 : $level1;
                    $path = 'zhaopin/' . $path . '/';

                    if ($path == 'zhaopin/xuqiuxueke/') {
                        $h5Host    = Yii::$app->params['h5Host'];
                        $h5FullUrl = 'http://' . $h5Host . '/major.html';
                        header("Location: $h5FullUrl");

                        exit;
                    }

                    $homeColumn   = HomeColumn::findOne(['path' => $path]);
                    $homeColumnId = $homeColumn ? $homeColumn->id : 0;

                    if (!$homeColumnId) {
                        $this->notFound();
                    }

                    $h5Host    = Yii::$app->params['h5Host'];
                    $h5FullUrl = 'http://' . $h5Host . BaseHomeColumn::getDetailUrl($homeColumnId);
                    header("Location: $h5FullUrl");

                    exit;
                }

                // 完整的url
                $fullUrl = Yii::$app->request->getHostInfo() . Yii::$app->request->url;
                $pcHost  = Yii::$app->params['pcHost'];
                $h5Host  = Yii::$app->params['h5Host'];
                if ($pcHost && $h5Host) {
                    $h5FullUrl = str_replace($pcHost, $h5Host, $fullUrl);
                    $h5FullUrl = str_replace('www.', '', $h5FullUrl);
                    $this->redirect($h5FullUrl);
                }
            }
        } catch (\Exception $e) {
        }
    }

    public function logAll()
    {
        // $ua = Yii::$app->request->getUserAgent();
        //
        // Cache::lPush('ALL:USER_ACTIVE', json_encode([
        //     'ua'     => $ua,
        //     'ip'     => IpHelper::getIp(),
        //     'userId' => Yii::$app->user->id ?: 0,
        //     'time'   => CUR_DATETIME,
        //     'isAjax' => Yii::$app->request->isAjax ? 1 : 0,
        //     'url'    => Yii::$app->request->getHostInfo() . Yii::$app->request->url,
        // ]));
    }

    public function setActive()
    {
        // // 做一个简单的测试,把用户的id和现在的时间保存到缓存里面去,一段时间后再取出来,用于更新用户的活跃时间
        $userId   = Yii::$app->user->id;
        $actionId = Yii::$app->controller->action->uniqueId;

        // 获取当前路由
        // $route = Yii::$app->controller->route;
        // // 判断不是登录的路由
        // bb($route);

        // 拿用户的最近一次登录信息
        // $lastLoginTime = BaseMember::findOneVal(['id' => $userId], 'last_login_time');
        //
        // // 转时间搓
        // $lastLoginTime = strtotime($lastLoginTime);
        // if ($lastLoginTime <= 1701340480) {
        //     // 退出登录
        //     Yii::$app->user->logout();
        //     // 判断是ajax请求
        //     if (Yii::$app->request->isAjax) {
        //         echo json_encode([
        //             'code'   => 403,
        //             'result' => 0,
        //             'msg'    => '请您先进行登录后再操作',
        //         ]);
        //         exit;
        //     } else {
        //         $this->redirect(UrlHelper::to(['home/index']));
        //     }
        //     return;
        // }

        if ($userId && $actionId) {
            $key  = Cache::ALL_RESUME_ACTION_CONTROLLER_KEY;
            $time = CUR_TIMESTAMP;
            // 写集合
            Cache::zadd($key, $time, $userId);
        }
    }
}
